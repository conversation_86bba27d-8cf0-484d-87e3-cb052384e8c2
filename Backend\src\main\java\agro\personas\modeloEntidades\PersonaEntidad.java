
package agro.personas.modeloEntidades;

import java.util.List;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonManagedReference;

import agro.establecimientos.modeloEntidades.EstablecimientoEntidad;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity(name = "agricultor")
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PersonaEntidad {

    @Id
    @GeneratedValue
    private UUID id;

    @Column(name = "razon_social")
    private String razonSocial;

    @Column(name = "direccion")
    private String direccion;

    @Column(name = "telefono")
    private String telefono;

    @Column(name = "mail")
    private String mail;

    @Column(name = "lugar")
    private String lugar;

    @Column(name = "cond_frente_iva")
    private String condFrenteIva;

    @Column(name = "documento")
    private String documento;

    @OneToMany(mappedBy = "persona", cascade = CascadeType.ALL)
    @JsonManagedReference
    private List<EstablecimientoEntidad> establecimientos;

}
